# ciba-participant

### Build package

```bash
make build
```

save path to wheel file, like `dist/ciba_participant-0.1.0-py3-none-any.whl` to env var using pwd

```bash
export CIBA_PARTICIPANT_WHEEL=`pwd -P`/dist/ciba_participant-0.1.0-py3-none-any.whl
```

### Install package

Select and activate your venv, like

```shell
source <path_to_venv>/bin/activate
```

Then install the package

```bash
uv run pip install $CIBA_PARTICIPANT_WHEEL
```

### Run migrations

Before doing that, make sure you have up and running local db, you might use one from docker-compose

```shell
make migrate-gen
make migrate
```

### Run docker-compose locally

For OS X:

```shell
docker-compose up -d
```
