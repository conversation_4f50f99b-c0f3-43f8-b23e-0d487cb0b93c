import logging
from uuid import UUID
from typing import Optional

import httpx

from ciba_participant.chat_api.constants import PARTICIPANT_TYPE, X_AUTH_KEY
from ciba_participant.settings import get_settings

settings = get_settings()


class FailedToAssignParticipantToChat(Exception):
    """Group chat_api assigning exception.

    Exception raised when participant could not be assigned to a group chat_api.
    """

    message: str = "Could not assign a participant to a group chat_api."


class FailedToRemoveParticipantFromChat(Exception):
    """Group chat_api removal exception.

    Exception raised when participant could not be removed from a group chat_api.
    """

    message: str = "Could not remove a participant from a group chat_api."


class FailedToDeleteConversation(Exception):
    """Group chat_api removal exception.

    Exception raised when conversation could not be deleted from a chat_api.
    """

    message: str = (
        "Could not delete a conversation from chat_api. Contact tech team for support."
    )


ADD_TO_GROUP_CHAT_MUTATION = """
    mutation MyMutation(
        $uniqueName: String!
        $participant: ConversationParticipantInput!
        ) {
      addParticipantToConversation(
        uniqueName: $uniqueName
        participant: $participant
      ){
        ... on ConversationType {
          id
        }
        ... on ConversationDneError {
          __typename
        }
      }
    }
"""

REMOVE_FROM_GROUP_CHAT_MUTATION = """
    mutation MyMutation(
        $uniqueName: String!
        $participantId: UUID!
        ) {
      removeParticipantFromConversation(
        uniqueName: $uniqueName
        participantId: $participantId
      ){
        ... on ConversationType {
          id
        }
        ... on ConversationDneError {
          __typename
        }
      }
    }
"""

CREATE_GROUP_CHAT_MUTATION = """
    mutation MyMutation(
        $friendlyName: String!
        $uniqueName: String!
        $participants: [ConversationParticipantInput!]!
        ) {
      createGroupChat(
        friendlyName: $friendlyName
        participants: $participants
        uniqueName: $uniqueName
      ){
        ... on ConversationType {
          id
        }
      }
    }
"""

DELETE_CONVERSATION = """
    mutation MyMutation(
        $uniqueName: String!
        ) {
      deleteConversation(
        uniqueName: $uniqueName
      )
    }
"""


GET_CHAT_TOKEN_QUERY = """
    query GetToken(
        $chatIdentity: String!
        $adminId: String!
        ) {
      token(chatIdentity: $chatIdentity, adminId: $adminId) {
        ... on TokenType {
          token
          chatIdentity
        }
        ... on ParticipantDneError {
          message
        }
        ... on ParticipantNotInConversationError {
          message
        }
      }
    }
"""


async def assign_participant_to_chat(
    group_unique_name: str,
    participant_id: UUID,
    chat_identity: str,
    participant_type: str = PARTICIPANT_TYPE,
) -> dict:
    """Add participant to chat_api."""
    async with httpx.AsyncClient() as request_client:
        result = await request_client.post(
            f"{settings.CHAT_API_HOST.strip('/')}/graphql",
            json={
                "query": ADD_TO_GROUP_CHAT_MUTATION,
                "variables": {
                    "uniqueName": group_unique_name,
                    "participant": {
                        "type": participant_type,
                        "id": str(participant_id),
                        "chatIdentity": chat_identity,
                    },
                },
            },
            headers={X_AUTH_KEY: settings.CHAT_API_KEY},
            timeout=60,
        )

    if result.status_code != 200 or "errors" in result.json():
        logging.error(
            "Could not assign participant to chat_api group. "
            "Status code: %s. Content: %s",
            result.status_code,
            result.content,
        )
        raise FailedToAssignParticipantToChat()
    return result.json()


async def remove_participant_from_chat(
    group_unique_name: str,
    participant_id: UUID,
) -> dict:
    """Remove participant from chat_api."""
    async with httpx.AsyncClient() as request_client:
        result = await request_client.post(
            f"{settings.CHAT_API_HOST.strip('/')}/graphql",
            json={
                "query": REMOVE_FROM_GROUP_CHAT_MUTATION,
                "variables": {
                    "uniqueName": group_unique_name,
                    "participantId": str(participant_id),
                },
            },
            headers={X_AUTH_KEY: settings.CHAT_API_KEY},
            timeout=60,
        )

    if result.status_code != 200 or "errors" in result.json():
        logging.error(
            "Could not remove participant from chat_api group. "
            "Status code: %s. Content: %s",
            result.status_code,
            result.content,
        )
        raise FailedToRemoveParticipantFromChat()
    return result.json()


async def create_group_chat(group_unique_name: str, friendly_name: str) -> dict:
    """Create group chat_api."""
    async with httpx.AsyncClient() as request_client:
        result = await request_client.post(
            f"{settings.CHAT_API_HOST.strip('/')}/graphql",
            json={
                "query": CREATE_GROUP_CHAT_MUTATION,
                "variables": {
                    "uniqueName": group_unique_name,
                    "friendlyName": friendly_name,
                    "participants": [],
                },
            },
            headers={X_AUTH_KEY: settings.CHAT_API_KEY},
            timeout=60,
        )

    if result.status_code != 200 or "errors" in result.json():
        logging.error(
            "Could not remove participant from chat_api group. "
            "Status code: %s. Content: %s",
            result.status_code,
            result.content,
        )
        raise FailedToAssignParticipantToChat()
    return result.json()


async def delete_conversation(unique_name: str) -> dict:
    """Delete conversation from chat_api."""
    async with httpx.AsyncClient() as request_client:
        result = await request_client.post(
            f"{settings.CHAT_API_HOST.strip('/')}/graphql",
            json={
                "query": DELETE_CONVERSATION,
                "variables": {
                    "uniqueName": unique_name,
                },
            },
            headers={X_AUTH_KEY: settings.CHAT_API_KEY},
            timeout=60,
        )

    if result.status_code != 200 or "errors" in result.json():
        logging.error(
            "Could not delete conversation from chat_api. Status code: %s. Content: %s",
            result.status_code,
            result.content,
        )
        raise FailedToDeleteConversation()

    return result.json()


async def get_token(
    chat_identity: str, admin_id: Optional[str] = None
) -> (httpx.Response | None, dict | None):
    errors = None
    result = None
    try:
        async with httpx.AsyncClient() as request_client:
            result = await request_client.post(
                f"{settings.CHAT_API_HOST.strip('/')}/graphql",
                json={
                    "query": GET_CHAT_TOKEN_QUERY,
                    "variables": {"chatIdentity": chat_identity, "adminId": admin_id},
                },
                headers={X_AUTH_KEY: settings.CHAT_API_KEY},
                timeout=60,
            )
        if result.status_code != 200 or "errors" in result.json():
            logging.error(
                "Could not remove participant from chat_api group. "
                "Status code: %s. Content: %s",
                result.status_code,
                result.content,
            )
            return result, result.json()
        return result, errors
    except httpx.RequestError as exc:
        raise RuntimeError(f"Error occurred while requesting chat API: {exc}")
    except httpx.HTTPStatusError as exc:
        raise ValueError(
            f"Error response {exc.response.status_code} from chat API: {exc.response.text}"
        )
