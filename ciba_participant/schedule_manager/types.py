from datetime import datetime
from enum import IntEnum
from typing import Optional

from pydantic import BaseModel


class ZoomRecurrenceType(IntEnum):
    DAILY = 1
    WEEKLY = 2
    MONTHLY = 3


class Occurrence(BaseModel):
    id: str
    start_time: datetime
    duration: int


class BaseZoomMeeting(BaseModel):
    meeting_id: str
    start_url: str
    join_url: str
    host_email: str


class CreateZoomMeetingOutput(BaseZoomMeeting):
    occurrences: list[Occurrence]


class GetZoomMeetingOutput(BaseZoomMeeting):
    type: int
    alternative_hosts: str
    topic: str
    time_zone: str
    occurrences: Optional[list[Occurrence]] = None


class GetRecordingOutput(BaseModel):
    recording_url: str


class ScheduleManagerError(Exception): ...
