from datetime import datetime, date, timedelta
from typing import Optional


def validate_date_range(
    start_date: Optional[datetime | date], end_date: Optional[datetime | date]
):
    """
    Method to validate a provided date range
    :param start_date: start date
    :param end_date: end date
    :raises: ValueError with invalid date range message
    """
    if start_date and end_date and start_date > end_date:
        raise ValueError("Start date cannot be greater than end date.")


def validate_date_range_duration(
    start_date: datetime | date, end_date: datetime | date, max_duration: timedelta
):
    """
    Method to validate a date range duration
    :param start_date: range start date
    :param end_date: range end date
    :param max_duration: max duration allowed for date range
    :raises: ValueError with invalid duration message
    """
    range_duration = end_date - start_date

    if range_duration > max_duration:
        raise ValueError(
            f"Range length cannot be greater than max duration allowed ({max_duration})."
        )
