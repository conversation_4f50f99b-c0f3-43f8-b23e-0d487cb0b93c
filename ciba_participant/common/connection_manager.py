from contextlib import asynccontextmanager
from contextvars import ContextVar
from functools import cache
from typing import NotRequired, TypedDict

from psycopg import AsyncConnection
from psycopg.conninfo import make_conninfo
from psycopg_pool import AsyncConnectionPool, AsyncNullConnectionPool
from pydantic import <PERSON><PERSON><PERSON>pter
from starlette.types import AS<PERSON><PERSON><PERSON>, Receive, Scope, Send

connection_var: ContextVar[AsyncConnection] = ContextVar("conn")


class ConnInfo(TypedDict):
    dbname: str
    user: str
    password: str
    host: str
    port: NotRequired[int]


class PoolOptions(TypedDict):
    min_size: int
    max_size: int


class ConnectionManager:
    _pool: AsyncConnectionPool | AsyncNullConnectionPool

    def __init__(
        self,
        conn_info: ConnInfo,
        *,
        use_pooling=False,
        pool_options: PoolOptions | None = None,
    ) -> None:
        _conn_info = TypeAdapter(ConnInfo).validate_python(conn_info)

        if use_pooling:
            _pool_options = pool_options or PoolOptions(min_size=1, max_size=5)
            _pool_options.setdefault("min_size", 1)
            _pool_options.setdefault("max_size", 5)
            _pool_options = TypeAdapter(PoolOptions).validate_python(_pool_options)
            pool_class = AsyncConnectionPool
        else:
            _pool_options = PoolOptions(min_size=0, max_size=0)
            pool_class = AsyncNullConnectionPool

        conn_info_str = make_conninfo(**_conn_info)

        kwargs = {"autocommit": True}

        self._pool = pool_class(
            conn_info_str,
            **_pool_options,
            open=False,
            kwargs=kwargs,
        )

    async def __aenter__(self):
        await self._pool.open()
        return self

    async def __aexit__(self, exc_type, exc, tb):
        await self._pool.close()

    @cache
    def asgi_middleware(self):
        """
        You are required to bind this middleware to an ASGI application.

        In FastAPI it could be done like so:
        ```py
        app = FastAPI()

        app.add_middleware(ConnectionManager(conn_info).asgi_middleware())
        ```

        The other way is to instantiate this class as a context manager, you won't be
        required to do this except for testing and one-off scripts:
        ```py
        async with ConnectionManager(conn_info) as cm:
            async with cm.connection() as conn:
                ...
        ```
        """

        class Middleware:
            def __init__(this, app: ASGIApp):
                this.app = app

            async def __call__(this, scope: Scope, receive: Receive, send: Send):
                async def receive_wrapper():
                    message = await receive()

                    if message["type"] == "lifespan.startup":
                        await self._pool.open()
                    elif message["type"] == "lifespan.shutdown":
                        await self._pool.close()

                    return message

                if scope["type"] in ["http", "websocket"]:
                    async with self.use_connection():
                        await this.app(scope, receive, send)
                        return
                elif scope["type"] == "lifespan":
                    await this.app(scope, receive_wrapper, send)
                    return

                await this.app(scope, receive, send)

        return Middleware

    @asynccontextmanager
    async def use_connection(self):
        async with self._pool.connection() as conn:
            token = connection_var.set(conn)
            try:
                yield conn
            finally:
                connection_var.reset(token)

    @staticmethod
    async def _execute(query):
        conn = connection_var.get()
        return await conn.execute(query)

    @staticmethod
    def connection():
        return connection_var.get()
