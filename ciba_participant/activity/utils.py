from datetime import datetime
from uuid import UUID

import pendulum

from ciba_participant.cohort.models import CohortMembers
from ciba_participant.log.logging import logger
from ciba_participant.participant.exceptions import ParticipantNotInCohortException


async def determine_activity_date(participant_id: UUID) -> datetime:
    """
    Determines the activity date for a participant based on their cohort start date.

    If the participant belongs to a cohort that starts in the future, the cohort's start date
    is used as the activity date. Otherwise, the current date is used.

    Args:
        participant_id (UUID): The unique identifier of the participant.

    Returns:
        datetime: The determined activity date.

    Raises:
        ParticipantNotInCohort: If the participant is not associated with any cohort.
    """
    cohort_member = (
        await CohortMembers.filter(participant_id=participant_id)
        .prefetch_related("cohort")
        .first()
    )

    if not cohort_member:
        logger.warning(f"Participant with ID {participant_id} not found in any cohort")
        raise ParticipantNotInCohortException("Participant is not in a cohort")

    activity_date = pendulum.now()
    cohort_start_date = cohort_member.cohort.started_at

    if cohort_start_date > activity_date:
        activity_date = pendulum.datetime(
            cohort_start_date.year,
            cohort_start_date.month,
            cohort_start_date.day,
            activity_date.hour,
            activity_date.minute,
            activity_date.second,
        ).add(days=1)

    return activity_date
