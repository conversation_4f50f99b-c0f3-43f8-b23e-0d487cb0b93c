from enum import StrEnum, auto
from typing import TypedDict
from uuid import uuid4

from pydantic import UUID4, Field

from .activity_event import ActivityEvent


class MassUnit(StrEnum):
    KG = auto()
    LB = auto()


class WeightRegistrationDevice(StrEnum):
    WHITINGS = auto()
    # TODO: add missing devices
    UNKNOWN = auto()


class WeightRegistrationPayload(TypedDict):
    weight: float
    units: MassUnit


class DeviceWeightRegistrationPayload(WeightRegistrationPayload):
    device: WeightRegistrationDevice


class WeightRegistrationEvent(ActivityEvent[WeightRegistrationPayload]):
    activity_type: str = Field(default="weight_registration")


class AutomaticWeightRegistrationPayload(WeightRegistrationPayload):
    device: WeightRegistrationDevice


class DeviceWeightRegistrationEvent(ActivityEvent[DeviceWeightRegistrationPayload]):
    name: str = "weight_automatically_registered"
    activity_type: str = "weight_registration"


class ManualWeightRegistrationEvent(ActivityEvent[WeightRegistrationPayload]):
    name: str = "weight_manually_registered"
    activity_type: str = "weight_registration"


def create_manual_weight_registration_event(
    *,
    weight: float,
    units: MassUnit,
    participant_id: UUID4,
    activity_id: UUID4 | None = None,
) -> ManualWeightRegistrationEvent:
    """
    Factory for creating events of manually registered weight data
    """
    return ManualWeightRegistrationEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload={"weight": weight, "units": units},
    )


def create_device_weight_registration_event(
    *,
    weight: float,
    units: MassUnit,
    device: WeightRegistrationDevice = WeightRegistrationDevice.UNKNOWN,
    participant_id: UUID4,
    activity_id: UUID4 | None = None,
) -> DeviceWeightRegistrationEvent:
    """
    Factory for creating events of weight data registered from measurement devices
    """
    return DeviceWeightRegistrationEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload={"weight": weight, "units": units, "device": device},
    )
