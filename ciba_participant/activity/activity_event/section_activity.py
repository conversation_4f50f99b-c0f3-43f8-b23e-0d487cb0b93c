from uuid import uuid4

from pydantic import UUID4

from .activity_event import ActivityEvent


class DocumentOpenedEvent(ActivityEvent[None]):
    name: str = "document_opened"
    activity_type: str = "section_activity"


def create_document_opened_event(
    *, participant_id: UUID4, activity_id: UUID4 | None = None
) -> DocumentOpenedEvent:
    """
    Factory for creating events of manually registered weight data
    """
    return DocumentOpenedEvent(
        participant_id=participant_id,
        activity_id=activity_id or uuid4(),
        payload=None,
    )
