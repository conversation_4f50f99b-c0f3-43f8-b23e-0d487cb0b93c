import math
from datetime import datetime
from typing import Optional, List
from uuid import UUID

from tortoise.queryset import Q
from pydantic import BaseModel

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.common.validations.dates import validate_date_range
from ciba_participant.content_library.enums import ContentMaterialStatus, MaterialTag
from ciba_participant.content_library.models import ContentMaterial


class MaterialFilters(BaseModel):
    tags: Optional[List[MaterialTag]] = None
    activity_types: Optional[List[ParticipantActivityEnum]] = None
    programs: Optional[List[UUID]] = None
    program_id: Optional[UUID] = None
    status: Optional[ContentMaterialStatus] = None
    search: Optional[str] = None
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None
    favorites_for: Optional[UUID] = None


class MaterialPaginatedList(BaseModel):
    total: int
    total_pages: int
    items: List


def validate_filters(filters: MaterialFilters = None):
    """
    Method to validate the provided filters.
    """
    if filters:
        validate_date_range(filters.start_date, filters.end_date)

        if filters.program_id and filters.programs:
            raise ValueError(
                "Combination of program_id and programs filters is not supported."
            )


def get_filters(filters: MaterialFilters = None) -> List[Q]:
    """
    Method to get a list of query filters to be applied.
    """
    if filters is None:
        return []

    actual_filters = []

    if filters.activity_types:
        actual_filters.append(
            Q(activity_types__activity_type__in=filters.activity_types)
        )
    if filters.programs:
        actual_filters.append(Q(programs__program_id__in=filters.programs))
    if filters.tags:
        actual_filters.append(Q(tags__tag__in=filters.tags))
    if filters.program_id:
        actual_filters.append(Q(programs__program_id=filters.program_id))
    if filters.status:
        actual_filters.append(Q(status=filters.status))
    if filters.start_date:
        actual_filters.append(Q(created_at__gte=filters.start_date))
    if filters.end_date:
        actual_filters.append(Q(created_at__lte=filters.end_date))
    if filters.search:
        actual_filters.append(
            Q(
                Q(title__icontains=filters.search)
                | Q(description__icontains=filters.search)
            )
        )
    if filters.favorites_for:
        actual_filters.append(
            Q(
                Q(interactions__is_favorite=True)
                & Q(interactions__participant_id=filters.favorites_for)
            )
        )

    return actual_filters


def get_prefetch_related_data(filters: MaterialFilters = None) -> List[str]:
    """
    Method to get the related data to prefetch
    """
    prefetch_data = ["programs", "activity_types", "tags"]

    if filters and filters.favorites_for:
        prefetch_data.append("interactions")

    return prefetch_data


async def process(page: int = 1, per_page: int = 10, filters: MaterialFilters = None):
    """
    Method to query the requested content material.
    """
    validate_filters(filters)

    query_filters = get_filters(filters)
    related_models = get_prefetch_related_data(filters)
    base_query = ContentMaterial.filter(*query_filters).prefetch_related(
        *related_models
    )

    total = len(await base_query.distinct().order_by("id").values_list("id", flat=True))
    total_pages = math.ceil(total / per_page)

    page_elements = await (
        base_query.all()
        .distinct()
        .order_by("-created_at")
        .offset((page - 1) * per_page)
        .limit(per_page)
    )

    return MaterialPaginatedList(
        total=total,
        total_pages=total_pages,
        items=page_elements,
    )
