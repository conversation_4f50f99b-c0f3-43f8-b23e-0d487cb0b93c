from datetime import timed<PERSON><PERSON>, datetime
from typing import Optional
from uuid import UUID

import pendulum
from ciba_participant import get_settings
from ciba_participant.common.signatures import sign_cloudfront_url
from ciba_participant.log.logging import logger
from ciba_participant.settings import ENV

EXPIRATION = 365
HYPHEN_REPLACEMENT = "-"
settings = get_settings()


def adjust_mime_type(url: Optional[str], form_id: Optional[str]) -> str:
    """
    Method to adjust mime types for not file based material.
    :param url: Material access url, used when is a url resource
    :param form_id: Material form id, used when is a typeform material
    """
    if form_id:
        return "form/type_form"

    if "youtube" in url.lower():
        return "url/video"

    if ".pdf" in url.lower():
        return "application/pdf"

    return "url/article"


def sanitize_file_name(file_name: Optional[str]) -> Optional[str]:
    """
    Method to sanitize file name for S3 storage.
    :param file_name: Original file name
    :return: The sanitized file name to be stored in S3
    """
    if not file_name:
        return None

    return (
        file_name.replace(" ", HYPHEN_REPLACEMENT)
        .replace("\u202f", HYPHEN_REPLACEMENT)
        .replace("+", HYPHEN_REPLACEMENT)
        .replace("=", "_")
        .replace("/", "~")
    )


def get_file_url(
    material_id: UUID, location: str, file_name: str
) -> (Optional[str], Optional[datetime]):
    """
    Method to get the file url for a content material.

    Args:
        material_id (UUID): The ID of the content material.
        location (str): The location of the file.
        file_name (str): The name of the file.

    Returns:
        Optional[str]: The pre-signed URL for the file, or None if an error occurs.
        Optional[datetime]: The expiration of the generated url.
    """
    expiration_date = pendulum.now("UTC") + timedelta(days=EXPIRATION)
    file_path = f"{location}/{material_id}-{file_name}"
    print(settings.ENV)

    if settings.ENV in [ENV.LOCAL, ENV.TEST]:
        return file_path, expiration_date

    try:
        link = sign_cloudfront_url(
            url=f"{settings.CONTENT_LIBRARY_DISTRIBUTION}/{file_path}",
            key_id=settings.CONTENT_LIBRARY_KEY_ID,
            sign_key=settings.CONTENT_LIBRARY_SIGN_KEY,
            expires_in=int(expiration_date.timestamp()),
        )

        return link, expiration_date
    except (ValueError, IndexError) as error:
        logger.exception(f"An error occurred while generating the signed URL: {error}")
        return None, None
