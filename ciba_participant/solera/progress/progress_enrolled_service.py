# from typing import Optional
#
# from ciba_participant.solera.api import HttpMethod, RequestType
# from ciba_participant.solera.api import SoleraApi
# from app.models.activity import Activity, MetricType
# from app.models.activity_progress import ActivityProgress, ActivityStatus
# from app.models.milestone import Milestone
# from app.models.milestone_progress import MilestoneProgress, MilestoneStatus
# from ciba_participant.participant.models import Participant
# from ciba_participant.solera.data_preparer import DataPreparer
# from ciba_participant.solera.progress.progress_service import SoleraProgress
# from ciba_participant.log.logging import logger
#
#
# class SoleraProgressEnrolledService(SoleraProgress):
#     """Service that keeps enroll flow logic for solera."""
#
#     def __init__(self, participant: Optional[Participant]) -> None:
#         self.participant = participant
#         self.milestone_name = "Enrollment"
#         self.activity_name = "enrollment"
#         self.milestone: Optional[Milestone] = None
#         self.activity: Optional[Activity] = None
#         self.activity_progres: Optional[ActivityProgress] = None
#
#     async def _get_data_by_name(self) -> None:
#         """Get data from db by name"""
#         self.milestone = await Milestone.filter(
#             name=self.milestone_name
#         ).get_or_none()
#
#         self.activity = await Activity.filter(
#             activity_type=self.activity_name
#         ).get_or_none()
#
#     async def add_activity(self) -> None:
#         await self._get_data_by_name()
#         if self.milestone and self.activity:
#             activity_progress = ActivityProgress(
#                 milestone=self.milestone,
#                 activity=self.activity,
#                 paricipant=self.participant,
#             )
#             await activity_progress.save()
#             self.activity_progres = activity_progress
#         else:
#             logger.error(
#                 f"Activity or milestone not found for participant {self.participant.id}"
#             )
#
#     async def add_milestone(self) -> None:
#         milestone = MilestoneProgress(
#             milestone=self.milestone,
#             paricipant=self.participant,
#             status=MilestoneStatus.WAIT,
#         )
#         await milestone.save()
#
#     async def send_activity(self) -> None:
#         if (
#             self.activity is not None
#             and self.activity.metric == MetricType.BOOLEAN
#         ):
#             activity_dict = {self.activity.activity_type.value: True}
#         else:
#             logger.error(
#                 f"Activity '{self.activity}' is not boolean for participant {self.participant.id}"
#             )
#
#         request_type = RequestType(HttpMethod.POST, "activities")
#         endpoint = RequestType.get_endpoint(request_type)
#         if self.activity_progres is not None:
#             prepare_request = await DataPreparer(
#                 self.participant
#             ).prepare_data(self.activity_progres.id, activity_dict)
#             logger.debug(prepare_request)
#             response = await SoleraApi().post(endpoint, prepare_request)
#             self.activity_progres.data = prepare_request
#             if "requestId" in response:
#                 self.activity_progres.referenceId = response["requestId"]
#                 self.activity_progres.status = ActivityStatus.SEND
#             else:
#                 self.activity_progres.error = response["validationError"]
#                 self.activity_progres.status = ActivityStatus.ERROR
#                 logger.error(
#                     f"Error in sending activity for participant {self.participant.id}"
#                 )
#
#             await self.activity_progres.save()
#         else:
#             logger.error(f"Activity progress is not created for participant {self.participant.id}")
