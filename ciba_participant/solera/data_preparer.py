import uuid
from datetime import datetime
from typing import Optional, Dict, Any, Union
from uuid import UUID

from ciba_participant.participant.models import SoleraParticipant
from ciba_participant.activity.models import SoleraParticipantActivityEnum
from pydantic import BaseModel, field_validator, Field


class SoleraData(BaseModel):
    userId: str  # solera_participant.solera_key
    enrollmentId: str  # solera_participant.enrollment_id
    programId: str  # solera_participant.solera_program_id
    referenceId: Optional[str] = None


class SoleraCorrectionPayload(SoleraData):
    timestamp: str
    data: Dict[str, Any] = {}
    eventType: str = "Correction"


class SoleraActivityPayload(SoleraData):
    """Solera activity payload"""

    timestamp: str
    activity_type: Optional[SoleraParticipantActivityEnum] = Field(exclude=True)
    data: Dict[str, Any] = {}

    @field_validator("data")
    @classmethod
    def set_data_based_on_activity_type(cls, v, values):
        activity_type = values.data.get("activity_type", None)
        if activity_type == SoleraParticipantActivityEnum.ENROLL:
            return {"enrollment": True}
        elif activity_type == SoleraParticipantActivityEnum.WEIGHT:
            return v
        elif activity_type == SoleraParticipantActivityEnum.PLAY:
            return {"videoWatched": 1}
        elif activity_type == SoleraParticipantActivityEnum.COACH:
            return {"coachInteraction": 1}
        elif activity_type == SoleraParticipantActivityEnum.MEALS:
            return {"mealsLogged": 1}
        elif activity_type == SoleraParticipantActivityEnum.RECIPES:
            return {"recipeEducation": 1}
        elif activity_type == SoleraParticipantActivityEnum.QUIZ:
            return {"quizCompleted": 1}
        elif activity_type == SoleraParticipantActivityEnum.ACTIVITY:
            return {"physicalActivity": 1}
        elif activity_type == SoleraParticipantActivityEnum.ARTICLE:
            return {"articleRead": 1}
        elif activity_type == SoleraParticipantActivityEnum.GROUP:
            return {"groupInteraction": 1}
        else:
            return {}

    class Config:
        fields = {
            "activity_type": {"exclude": True},
        }

    def __str__(self):
        return f"{self.userId} - {self.enrollmentId} - {self.referenceId} - {self.programId} - {self.timestamp} - {self.data}"


class SoleraActivitiesList(BaseModel):
    activities: list[SoleraActivityPayload] = []


class DataPreparer:
    """Prepare data for solera"""

    def __init__(self, participant: Optional[SoleraParticipant]) -> None:
        self.participant = participant

    async def prepare_correction_payload(
        self, reference_id: UUID, activity_type: SoleraParticipantActivityEnum
    ) -> SoleraActivitiesList:
        """Prepare activity payload and return it"""
        if self.participant is None:
            raise Exception("Participant not found")

        user_data = self.participant.solera_data()
        corretion_payload = SoleraCorrectionPayload(
            userId=user_data["userId"],
            enrollmentId=user_data["enrollmentId"],
            referenceId=reference_id,
            programId=user_data["programId"],
            timestamp=datetime.utcnow().isoformat(),
        )
        activities_data = SoleraActivitiesList(activities=[corretion_payload])

        return activities_data

    async def prepare_activity_list_payload(
        self,
        activity_type: SoleraParticipantActivityEnum,
        activity_id: str = None,
        activity: dict = None,
        timestamp: datetime = None,
    ) -> SoleraActivitiesList:
        """Prepare activity payload and return it"""
        if self.participant is None:
            raise Exception("Participant not found")

        activity_id = str(uuid.uuid4()) if not activity_id else activity_id
        user_data = self.participant.solera_data()
        timestamp = timestamp.isoformat() if timestamp else datetime.now().isoformat()

        activity_list = SoleraActivitiesList()
        activity_list.activities.append(
            SoleraActivityPayload(
                userId=str(user_data["userId"]),
                enrollmentId=str(user_data["enrollmentId"]),
                referenceId=str(activity_id),
                programId=str(user_data["programId"]),
                timestamp=timestamp,
                data=activity,
                activity_type=activity_type,
            )
        )
        return activity_list

    async def prepare_activity_payload(
        self,
        activity_type: SoleraParticipantActivityEnum,
        activity: dict = None,
        activity_id: str = None,
        timestamp: datetime = None,
    ) -> SoleraActivityPayload:
        """Prepare activity payload and return it"""
        if self.participant is None:
            raise Exception("Participant not found")

        activity_id = str(uuid.uuid4()) if not activity_id else activity_id
        user_data = self.participant.solera_data()
        timestamp = timestamp.isoformat() if timestamp else datetime.now().isoformat()
        activity = SoleraActivityPayload(
            userId=str(user_data["userId"]),
            enrollmentId=str(user_data["enrollmentId"]),
            referenceId=str(activity_id),
            programId=str(user_data["programId"]),
            timestamp=timestamp,
            data=activity,
            activity_type=activity_type,
        )
        return activity

    async def prepare_enrollemt_payload(
        self, activity_id: str, user_created_at: datetime, full: bool = False
    ) -> Union[SoleraActivityPayload, SoleraActivitiesList]:
        """Prepare activity payload and return it"""
        if self.participant is None:
            raise Exception("Participant not found")

        activity_id = str(uuid.uuid4()) if not activity_id else activity_id
        activity_payload = (
            await self.prepare_activity_payload(
                activity_id=activity_id,
                activity={"Enrollment": True},
                timestamp=user_created_at,
                activity_type=SoleraParticipantActivityEnum.ENROLL,
            )
            if not full
            else await self.prepare_activity_list_payload(
                activity_id=activity_id,
                activity={"Enrollment": True},
                timestamp=user_created_at,
                activity_type=SoleraParticipantActivityEnum.ENROLL,
            )
        )

        return activity_payload
