import datetime

import httpx
import time
from typing import Optional, List, Dict
from uuid import uuid4
from loguru import logger
from pydantic import BaseModel, Field

from ciba_participant.settings import ENV, get_settings

settings = get_settings()

SOLERA_BASE_URLS = {
    ENV.STG: {
        "activities": "https://solera-api-gateway-stg.azure-api.net/provider/v2/activities",
        "status": "https://solera-api-gateway-stg.azure-api.net/provider/v2/activities/batch/{requestId}/status",
        "enrolled": "https://solera-api-gateway-stg.azure-api.net/provider/v2/program/{programId}/{userId}/enrolled",
        "milestones": "https://solera-api-gateway-stg.azure-api.net/provider/v2/milestones/{programId}/{userId}",
    },
    ENV.PROD: {
        "activities": "https://api.soleranetwork.com/provider/v2/activities",
        "status": "https://api.soleranetwork.com/provider/v2/activities/batch/{requestId}/status",
        "enrolled": "https://api.soleranetwork.com/provider/v2/program/{programId}/{userId}/enrolled",
        "milestones": "https://api.soleranetwork.com/provider/v2/milestones/{programId}/{userId}",
    },
    ENV.DEV: {
        "activities": "https://solera-api-gateway-stg.azure-api.net/provider/v2/activities",
        "status": "https://solera-api-gateway-stg.azure-api.net/provider/v2/activities/batch/{requestId}/status",
        "enrolled": "https://solera-api-gateway-stg.azure-api.net/provider/v2/program/{programId}/{userId}/enrolled",
        "milestones": "https://solera-api-gateway-stg.azure-api.net/provider/v2/milestones/{programId}/{userId}",
    },
}


class UserInfo(BaseModel):
    solera_uuid: str = Field(alias="https://soleranetwork.com/solera_uuid")
    enrollmentId: str
    patientId: str
    programId: str
    programType: str
    milestoneStructureCode: str
    given_name: str
    family_name: str
    ethnicity: list
    gender: str
    birthdate: str
    heightFeet: int
    heightInches: int
    physicalActivityLevel: str
    weight: int
    phone_number: str
    email: str
    address: dict
    doNotText: Optional[bool] = True
    payorId: str
    insuranceType: Optional[str] = None
    languagePreference: str


class EnrollmentStatus(BaseModel):
    enrolled: bool
    disenrolledReason: Optional[str] = "No provided reason"
    disenrollmentDate: Optional[str] = "No mentioned date"


class EnrollmentStatusResp(BaseModel):
    data: EnrollmentStatus


class Milestone(BaseModel):
    name: str
    date: str


class MilestoneStatus(BaseModel):
    userId: str
    programId: str
    programStart: Optional[str]
    milestones: list[Milestone]


class SoleraActivityPayload(BaseModel):
    userId: str
    enrollmentId: str
    referenceId: str
    programId: str
    timestamp: str
    data: dict = {}


class SoleraActivitiesList(BaseModel):
    activities: list[SoleraActivityPayload] = []


class ActivityStatus(BaseModel):
    referenceId: str
    userId: str
    message: Optional[str]


class ActivityStatusData(BaseModel):
    status: str
    successes: list[ActivityStatus]
    errors: list[ActivityStatus]


class ActivityStatusResp(BaseModel):
    data: ActivityStatusData


class SubmitActivityResp(BaseModel):
    requestId: str


class ValidationError(BaseModel):
    fields: dict
    message: str
    status: str
    name: str


class SubmitActivity400Resp(BaseModel):
    validationError: ValidationError


class SoleraAPIAsyncClient:
    """
    Asynchronous client for interacting with Solera Connect API.

    Handles authentication and provides methods for user info retrieval,
    activity submission, activity status, enrollment status, milestone status,
    and activity correction.
    """

    def __init__(self, client_id: str, client_secret: str, environment: str):
        """
        Initializes the SoleraAPIAsyncClient.

        Args:
            client_id (str): The client ID for authentication.
            client_secret (str): The client secret for authentication.
            environment (str): The environment to use ('stg' or 'prod').
        """
        self.client_id = client_id
        self.client_secret = client_secret
        self.token = None
        self.token_expiry = None
        self.environment = environment
        self.base_urls = SOLERA_BASE_URLS

    async def authenticate(self):
        """
        Authenticates the client and retrieves an access token.



        :return {
                    access_token: '...',
                    expires_in: 57600,
                    scope: '...',
                    token_type: 'Bearer'
                }
        """
        auth_url = settings.SOLERA_AUTH_URL
        payload = {
            "client_id": self.client_id,
            "client_secret": self.client_secret,
            "audience": "wavePartnerAPI",
            "grant_type": "client_credentials",
        }
        async with httpx.AsyncClient() as client:
            response = await client.post(auth_url, json=payload)
            response_data = response.json()
            if "access_token" not in response_data:
                logger.error(f"Error authenticating with Solera API: {response_data}")
                response.raise_for_status()
            self.token = response_data["access_token"]
            self.token_expiry = (
                time.time() + response_data["expires_in"] - 100
            )  # Refresh a bit before expiry

    async def get_headers(self):
        """
        Retrieves the headers required for API requests, refreshing the token if necessary.

        Returns:
            dict: Headers including the Authorization bearer token.
        """
        if not self.token or time.time() > self.token_expiry:
            await self.authenticate()
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json",
        }

    async def request(self, method: str, url: str, data: Optional[dict] = None):
        """
        Makes an HTTP request to the Solera API and handles errors.

        Args:
            method (str): HTTP method ('GET', 'POST', etc.).
            url (str): The URL for the API endpoint.
            data (Optional[dict]): The data to send with the request (for POST, PUT, etc.).

        Returns:
            dict: The response data from the API.
        """
        headers = await self.get_headers()
        async with httpx.AsyncClient(timeout=httpx.Timeout(900)) as client:
            response = await client.request(method, url, headers=headers, json=data)
            if response.status_code >= 400:
                response.raise_for_status()
                logger.error(f"Error in Solera API request: {response.text}")
                return response
            return response.json()

    async def get_user_info(self, lookup_key: str) -> dict:
        """
        Retrieves information about a member associated with the lookup key.

        Args:
            lookup_key (str): The lookup key supplied from handoff.

        Returns:
            dict: The user information.
        """
        url = f"{settings.SOLERA_API_URL}/v2/userinfo/{lookup_key}"
        return await self.request("GET", url)

    async def submit_activity(self, activities: dict) -> dict:
        """
        Submits activity data for a member.

        Args:
            activities (list): List of activity data to submit. Each activity should include userId, enrollmentId,
                               referenceId, programId, timestamp, and activity-specific data.

        Returns:
            dict: The response from the activity submission.
        """
        url = self.base_urls[self.environment]["activities"]
        return await self.request("POST", url, data=activities)

    async def get_activity_status(self, request_id: str) -> dict:
        """
        Retrieves the status of a previously submitted activity batch.

        Args:
            request_id (str): The request ID returned from Activity Submission Endpoint.

        Returns:
            dict: The status of the submitted activity batch.
        """
        url = self.base_urls[self.environment]["status"].format(requestId=request_id)
        return await self.request("GET", url)

    async def get_enrollment_status(self, program_id: str, user_id: str) -> dict:
        """
        Retrieves the enrollment status of a member in a program.

        Args:
            program_id (str): Program ID returned from UserInfo Endpoint.
            user_id (str): Enrollment ID returned from UserInfo Endpoint.

        Returns:
            dict: The enrollment status of the member.
        """
        url = self.base_urls[self.environment]["enrolled"].format(
            programId=program_id, userId=user_id
        )
        return await self.request("GET", url)

    async def get_milestone_status(self, program_id: str, user_id: str) -> dict:
        """
        Retrieves the milestone status for a member enrolled in a program.

        Args:
            program_id (str): Program ID returned from UserInfo endpoint.
            user_id (str): Enrollment ID returned from UserInfo endpoint.

        Returns:
            dict: The milestone status of the member.
        """
        url = self.base_urls[self.environment]["milestones"].format(
            programId=program_id, userId=user_id
        )
        return await self.request("GET", url)

    async def correct_activity(self, activities_with_refs: List[Dict]):
        """
        Submits corrected activity data by marking the invalid data as entered-in-error.

        Args:
            activities_with_refs (List[Dict]): List of activities with their original reference IDs to correct.
                                               Each dict should include 'activity' (the corrected activity data)
                                               and 'original_reference_id' (the reference ID of the original activity).

        Returns:
            dict: The response from the corrected activity submission.


        {"activities": [
                {
                    "data": {},
                    "userId": "295e40b31b454433b4ca4084",
                    "eventType": "Correction",
                    "programId": "HWM",
                    "timestamp": "2024-03-02T14:40:44.791449",
                    "referenceId": "0dd94a30-958f-4d23-acba-20dd7574fae1",
                    "enrollmentId": "295e40b31b454433b4ca4084"
                }
            ]
        }
        """
        corrected_activities = []
        for activity_with_ref in activities_with_refs:
            original_reference_id = activity_with_ref["referenceId"]
            correction_activity = {
                "eventType": "Correction",
                "userId": activity_with_ref["userId"],
                "enrollmentId": activity_with_ref["enrollmentId"],
                "referenceId": original_reference_id,
                "programId": activity_with_ref["programId"],
                "timestamp": datetime.datetime.now().isoformat(),
                "data": {},
            }
            activity_with_ref["referenceId"] = str(uuid4())

            corrected_activities.append((activity_with_ref, correction_activity))

        return corrected_activities

    async def get_user_activity_data(self, enrollemnt_id: str):
        url = f"{self.base_urls[self.environment]['activities']}/{enrollemnt_id}"
        return await self.request("GET", url)
