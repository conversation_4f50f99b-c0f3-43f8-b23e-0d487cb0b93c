from datetime import datetime
from enum import Enum
from hashlib import md5
from random import shuffle
from string import ascii_uppercase
from typing import TYPE_CHECKING, Optional
from uuid import UUID, uuid4

from pydantic import BaseModel, ConfigDict
from tortoise import Model
from tortoise.fields import (
    BooleanField,
    CharEnumField,
    CharField,
    DatetimeField,
    ForeignKeyField,
    JSONField,
    ManyToManyField,
    ManyToManyRelation,
    ReverseRelation,
    UUIDField,
)
from tortoise.manager import Manager

from ciba_participant.common.models import TimestampMixin

if TYPE_CHECKING:
    from ciba_participant.activity.models import ParticipantActivity
    from ciba_participant.cohort.models import Cohor<PERSON>, CohortMembers
    from ciba_participant.participant.models import HeadsUpParticipant, ParticipantMeta


class ParticipantStatus(Enum):
    ACTIVE = "active"
    COMPLETED = "completed"
    PENDING = "pending"
    REJECTED = "rejected"
    DELETED = "deleted"


class ActiveParticipantsManager(Manager):
    """Participant manager to return active participants only."""

    def get_queryset(self):
        return (
            super(ActiveParticipantsManager, self)
            .get_queryset()
            .filter(status__not=ParticipantStatus.DELETED)
        )


class AllParticipantsManager(Manager):
    """Manager to return all participants, including deleted ones."""

    def get_queryset(self):
        return super(AllParticipantsManager, self).get_queryset()


class Participant(Model, TimestampMixin):
    """Participant database model."""

    id = UUIDField(primary_key=True, default=uuid4)
    email = CharField(max_length=255, unique=True)
    first_name = CharField(max_length=255)
    last_name = CharField(max_length=255)
    group_id = UUIDField(max_length=255)
    member_id = UUIDField(max_length=255)
    status = CharEnumField(ParticipantStatus, default=ParticipantStatus.PENDING)
    cognito_sub = UUIDField(null=True)
    medical_record = CharField(max_length=255, unique=True, null=True)
    is_test = BooleanField(default=False)
    last_reset = DatetimeField(null=True)

    activities: ReverseRelation["ParticipantActivity"]
    solera_participant: ReverseRelation["SoleraParticipant"]
    participant_meta: ReverseRelation["ParticipantMeta"]
    heads_up_participant: ReverseRelation["HeadsUpParticipant"]
    cohorts: ReverseRelation["CohortMembers"]

    active_participants = ActiveParticipantsManager()
    all_participants = AllParticipantsManager()

    @property
    def chat_identity(self) -> str:
        """Generate unique chat_api identity for a participant."""
        return md5(f"evolvdhealth_participant_{self.id}".encode()).hexdigest()

    @classmethod
    def generate_medical_record(
        cls, generated_date: datetime | None = None, length: int = 5
    ) -> str:
        """Generate unique medical record."""
        letters = list(ascii_uppercase)
        shuffle(letters)
        curr_date = generated_date or datetime.utcnow()
        year = curr_date.strftime("%y")
        rand_str = "".join(letters[0:length])
        day_of_year = curr_date.timetuple().tm_yday
        return f"{year}-{rand_str}-{day_of_year}"

    def full_name(self) -> str:
        """Return full name of a participant."""
        return f"{self.first_name} {self.last_name}".lstrip().rstrip()

    class Meta:
        table = "participants"
        manager = ActiveParticipantsManager()


class RawParticipant(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    email: str
    first_name: str
    last_name: str
    group_id: UUID
    member_id: UUID
    status: ParticipantStatus
    cognito_sub: Optional[UUID]
    medical_record: Optional[str]
    is_test: bool
    last_reset: Optional[datetime]
    chat_identity: str


class SoleraParticipant(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    participant = ForeignKeyField(
        "models.Participant", related_name="solera_participant"
    )
    solera_id = UUIDField(null=True)
    solera_key = CharField(max_length=255)
    solera_program_id = CharField(max_length=255)
    solera_enrollment_id = CharField(max_length=255)
    status = CharEnumField(ParticipantStatus, default=ParticipantStatus.ACTIVE)

    def solera_data(self) -> dict:
        """Return user data for solera."""
        solera_dict = {
            "userId": str(self.solera_key),
            "programId": self.solera_program_id,
            "enrollmentId": self.solera_enrollment_id,
        }
        return solera_dict

    class Meta:
        table = "solera_participants"


class HeadsUpParticipant(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    participant = ForeignKeyField(
        "models.Participant", related_name="heads_up_participant"
    )
    heads_up_token = CharField(max_length=255, null=True)
    heads_up_id = CharField(max_length=255, null=True)

    class Meta:
        table = "heads_up_participants"


class ParticipantMeta(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    participant = ForeignKeyField("models.Participant", related_name="participant_meta")
    metadata: dict = JSONField(null=True)  # type: ignore

    class Meta:
        table = "participant_meta"


class AutorizedRole(Enum):
    ADMIN = "admin"
    HEALTH_COACH = "health_coach"
    PROVIDER = "provider"


class ChatRole(Enum):
    PROVIDER = "provider"
    ADMIN = "admin"
    PATIENT = "patient"


role_to_participant_type = {
    AutorizedRole.HEALTH_COACH: ChatRole.PROVIDER,
    AutorizedRole.ADMIN: ChatRole.ADMIN,
    AutorizedRole.PROVIDER: ChatRole.PROVIDER,
    # Add more mappings as needed
}


class Authorized(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    email = CharField(max_length=255, unique=True)
    first_name = CharField(max_length=255)
    last_name = CharField(max_length=255)
    status = CharEnumField(ParticipantStatus, default=ParticipantStatus.PENDING)
    cognito_sub = UUIDField(null=True)
    is_test = BooleanField(default=False)
    role = CharEnumField(AutorizedRole, default=AutorizedRole.HEALTH_COACH)
    cohorts: ManyToManyRelation["Cohort"] = ManyToManyField(
        "models.Cohort", related_name="authorized_users"
    )
    webinars = ReverseRelation["Webinar"]
    alternative_host = BooleanField(default=False)
    # To provide the same chat identity for the same user from the ciba platform
    ciba_api_id = UUIDField(null=True)
    support_in_chat = BooleanField(default=False)
    classes_admin = BooleanField(default=False)
    content_admin = BooleanField(default=False)

    class Meta:
        table = "authorized"

    def full_name(self) -> str:
        """Return full name of a participant."""
        return f"{self.first_name} {self.last_name}".lstrip().rstrip()

    def full_admin(self) -> bool:
        """Check if the user has full admin rights."""
        is_admin = self.role is AutorizedRole.ADMIN
        return (
            is_admin
            and self.support_in_chat
            and self.classes_admin
            and self.content_admin
        )

    @property
    def chat_identity(self) -> str:
        """Generate unique chat_api identity for a participant."""
        return md5(f"evolvdhealth_provider_{self.api_id}".encode()).hexdigest()

    @property
    def api_id(self) -> UUID:
        """For some services like chat API we should use the old ID from ciba API(if exists) for consistency"""
        return self.ciba_api_id or self.id


class RawAuthorized(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    email: str
    first_name: str
    last_name: str
    status: ParticipantStatus
    cognito_sub: Optional[UUID]
    is_test: bool
    role: AutorizedRole
    chat_identity: str
    api_id: Optional[UUID]
    support_in_chat: bool
    classes_admin: bool = False
    content_admin: bool = False
