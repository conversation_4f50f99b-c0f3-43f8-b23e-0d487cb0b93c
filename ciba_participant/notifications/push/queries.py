from typing import Optional, List

import pendulum
from pydantic import BaseModel

from ciba_participant.activity.models import ParticipantActivityEnum
from ciba_participant.classes.models import Booking, BookingStatusEnum
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.participant.models import Participant, ParticipantStatus


class ParticipantPushOutput(BaseModel):
    email: str
    first_name: str
    last_name: str
    is_weight: Optional[bool] = False


def get_chat_query(chat_identity: str, chat_sid: str) -> str:
    """
    Return query that will look for participants in chat_db looking for conversation that,
    provider or admin wrote in.
    If it was not provider or admin, this query will not return any participants.

    :param chat_identity: chat_identity of the provider or admin
    :param chat_sid: sid of the conversation
    :return: query as a string
    """
    return f"""
        WITH target_participant AS (
        SELECT p.id
        FROM participant p
        WHERE p.chat_identity = '{chat_identity}'
        AND p.external_type IN ('admin', 'provider')
        ),
        target_conversation AS (
            SELECT cp.participant_id
            FROM conversation_participant cp
            WHERE cp.conversation_id IN (
                SELECT id
                FROM conversation
                where sid = '{chat_sid}'
            )
        )
        SELECT p.external_id
        FROM participant p
        WHERE p.id IN (SELECT participant_id FROM target_conversation)
        AND EXISTS (SELECT 1 FROM target_participant)
        """


async def get_start_module_participants() -> Optional[List[ParticipantPushOutput]]:
    """
    Retrieve participants whose program modules start today.

    Returns:
        Optional[List[ParticipantPushOutput]]: A list of participants with module starting today,
                                              or None if no participants are found.
    """
    # Get the current day's start and end time using Pendulum for better clarity
    today = pendulum.today("UTC")
    start_of_day = today.start_of("day")  # Start of today
    end_of_day = today.end_of("day")  # End of today

    # Query program modules that start today
    program_modules = (
        await CohortProgramModules.filter(
            started_at__gte=start_of_day, started_at__lt=end_of_day
        )
        .prefetch_related("cohort__participants__participant_meta")
        .all()
    )

    # Collect all participants from the filtered program modules
    participants = []
    for module in program_modules:
        cohort_participants = module.cohort.participants
        if cohort_participants:
            cohort_participants = [
                participant
                for participant in cohort_participants
                if participant.status == ParticipantStatus.ACTIVE
            ]
        participants.extend(cohort_participants)

    # Return the processed participant information if any
    if participants:
        return [
            ParticipantPushOutput(
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
                is_weight=(
                    participant.participant_meta[0].metadata.get("is_weight", False)
                    if participant.participant_meta
                    else False
                ),
            )
            for participant in participants
        ]
    return None


async def get_participants_no_weight_activity() -> Optional[
    List[ParticipantPushOutput]
]:
    """
    Retrieve participants who have not entered their weight in the last 7 days.

    Returns:
        Optional[List[ParticipantPushOutput]]: A list of participants without weight activity,
                                              or None if all participants have logged weight.
    """
    target_date = pendulum.now("UTC").subtract(days=7)
    start_of_day = target_date.start_of("day")
    end_of_day = target_date.end_of("day")

    participants = await Participant.all().prefetch_related(
        "participant_meta", "activities"
    )

    if not participants:
        return None

    # Process participants and check if their last weight activity was exactly 7 days ago
    result = []
    for participant in participants:
        # Sort weight activities by created_at descending
        weight_activities = sorted(
            [
                activity
                for activity in participant.activities
                if activity.activity_type == ParticipantActivityEnum.WEIGHT
            ],
            key=lambda act: act.created_at,
            reverse=True,
        )

        # Check the most recent weight activity
        if (
            weight_activities
            and start_of_day <= weight_activities[0].created_at < end_of_day
        ):
            result.append(
                ParticipantPushOutput(
                    email=participant.email,
                    first_name=participant.first_name,
                    last_name=participant.last_name,
                    is_weight=(
                        participant.participant_meta[0].metadata.get("is_weight", False)
                        if participant.participant_meta
                        else False
                    ),
                )
            )

    return result if result else None


async def get_participants(participant_id: list) -> list[ParticipantPushOutput] | None:
    """Get participants by external id's from chat_db."""
    participants = (
        await Participant.filter(id__in=participant_id)
        .prefetch_related("participant_meta")
        .all()
    )
    if participants:
        return [
            ParticipantPushOutput(
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
                is_weight=(
                    participant.participant_meta[0].metadata.get("is_weight", False)
                    if participant.participant_meta
                    else False
                ),
            )
            for participant in participants
        ]

    return None


async def get_participants_with_class_tomorrow(
    timezone: str = "UTC",
) -> List[ParticipantPushOutput]:
    """
    Retrieve unique participants who have classes scheduled tomorrow in the specified timezone.

    Args:
        timezone (str): The timezone to use for calculating tomorrow's date.

    Returns:
        List[ParticipantPushOutput]: A list of unique participants with classes scheduled tomorrow.
        Each participant appears only once in the list.
    """
    # Use PT timezone instead of UTC
    now = pendulum.now(timezone)
    tomorrow_start = now.add(days=1).start_of("day")
    tomorrow_end = tomorrow_start.end_of("day")

    # Convert PT times back to UTC for database query
    tomorrow_start_utc = tomorrow_start.in_timezone("UTC")
    tomorrow_end_utc = tomorrow_end.in_timezone("UTC")

    bookings = (
        await Booking.filter(
            live_session__meeting_start_time__gte=tomorrow_start_utc,
            live_session__meeting_start_time__lt=tomorrow_end_utc,
            status=BookingStatusEnum.BOOKED,
        )
        .prefetch_related("live_session", "participant")
        .all()
    )

    unique_participants = {}

    for booking in bookings:
        participant = booking.participant
        if participant.email not in unique_participants:
            unique_participants[participant.email] = ParticipantPushOutput(
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
            )

    return list(unique_participants.values())


async def get_participants_with_class_in_next_hour() -> List[ParticipantPushOutput]:
    """
    Retrieve unique participants who have classes scheduled within the next hour,
    using strict time boundaries to prevent duplicate notifications in 5-minute intervals.

    Returns:
        List[ParticipantPushOutput]: A list of unique participants with classes in the next hour.
    """
    now = pendulum.now("UTC")

    current_minute = now.minute
    rounded_minute = (current_minute // 5) * 5
    current_boundary = now.replace(minute=rounded_minute, second=0, microsecond=0)

    class_window_start = current_boundary.add(minutes=60)
    class_window_end = current_boundary.add(minutes=65)

    bookings = (
        await Booking.filter(
            live_session__meeting_start_time__gte=class_window_start,
            live_session__meeting_start_time__lt=class_window_end,
            status=BookingStatusEnum.BOOKED,
        )
        .prefetch_related("live_session", "participant")
        .all()
    )

    unique_participants = {}

    for booking in bookings:
        participant = booking.participant
        if participant.email not in unique_participants:
            unique_participants[participant.email] = ParticipantPushOutput(
                email=participant.email,
                first_name=participant.first_name,
                last_name=participant.last_name,
            )

    return list(unique_participants.values())
