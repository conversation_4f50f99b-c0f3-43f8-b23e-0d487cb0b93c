from datetime import datetime
from typing import Optional, TYPE_CHECKING
from uuid import UUID, uuid4
from enum import StrEnum, IntEnum, auto

from pydantic import BaseModel, ConfigDict
from tortoise.fields import (
    CharEnumField,
    BooleanField,
    CharField,
    DatetimeField,
    UUIDField,
    IntField,
    IntEnumField,
    ForeignKeyNullableRelation,
    ForeignKeyField,
    ForeignKeyRelation,
    ReverseRelation,
    SET_NULL,
    CASCADE,
)
from tortoise.models import Model

from ciba_participant.common.models import TimestampMixin
from ciba_participant.participant.models import RawAuthorized, RawParticipant

if TYPE_CHECKING:
    from ciba_participant.participant.models import (
        Authorized,
    )


class TopicEnum(StrEnum):
    FOOD = auto()
    EDUCATIONAL = auto()
    ACTIVITY = auto()
    HEALTH_AND_WELLNESS = auto()
    MENTAL_HEALTH = auto()
    INTRO_SESSION = auto()


class RecurrenceEnum(StrEnum):
    WEEKLY = auto()
    BI_WEEKLY = auto()
    TRI_WEEKLY = auto()
    MONTHLY = auto()


class BookingStatusEnum(StrEnum):
    BOOKED = auto()
    ATTENDED = auto()
    WATCHED_RECORDING = auto()
    CANCELED = auto()


class TimeOfDayEnum(StrEnum):
    MORNING = auto()
    AFTERNOON = auto()
    EVENING = auto()


class MeetingTypeEnum(IntEnum):
    INSTANT = 1
    SCHEDULED = 2
    RECURRING_WITH_FIXED_TIME = 8


class Booking(Model, TimestampMixin):
    id = UUIDField(primary_key=True)

    live_session_id: UUID
    live_session = ForeignKeyField("models.LiveSession", on_delete=CASCADE)

    participant_id: UUID
    participant = ForeignKeyField("models.Participant", on_delete=CASCADE)

    status = CharEnumField(enum_type=BookingStatusEnum)

    class Meta:
        table = "booking"
        unique_together = (("live_session", "participant"),)
        ordering = ["created_at"]


class RawBooking(BaseModel):
    """
    Represents a raw booking entity in the system.
    """

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    live_session_id: UUID
    participant_id: UUID
    status: BookingStatusEnum


class FullBooking(RawBooking):
    live_session: Optional["FullLiveSession"] = None
    participant: Optional["RawParticipant"] = None


class LiveSession(Model, TimestampMixin):
    id = UUIDField(primary_key=True, default=uuid4)
    title = CharField(max_length=255, null=True)
    description = CharField(max_length=500, null=True)
    meeting_start_time = DatetimeField()

    webinar_id: UUID
    webinar: ForeignKeyRelation["Webinar"] = ForeignKeyField(
        "models.Webinar", related_name="sessions", null=False
    )

    meeting_type = IntEnumField(
        MeetingTypeEnum, default=MeetingTypeEnum.RECURRING_WITH_FIXED_TIME.value
    )

    zoom_id = CharField(max_length=255, null=True)
    zoom_occurrence_id = CharField(max_length=255, null=True)

    timezone = CharField(max_length=255, default="UTC", null=True)
    has_conflict = BooleanField(default=False, null=True)

    recording_url = CharField(max_length=255, null=True)

    use_custom_meeting_link = BooleanField(default=False, null=True)
    custom_meeting_link = CharField(max_length=255, null=True, default="")

    bookings: ReverseRelation["Booking"]

    @property
    def zoom_link(self) -> Optional[str]:
        if not self.zoom_id:
            return None

        return f"https://us02web.zoom.us/j/{self.zoom_id}"

    class Meta:
        table = "live_session"
        ordering = ["meeting_start_time"]


class RawLiveSession(BaseModel):
    """
    Raw representation of a live session as delivered from database with no relationships
    """

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    title: Optional[str] = None
    description: Optional[str] = None
    meeting_start_time: datetime
    timezone: str = "UTC"
    has_conflict: bool = False
    meeting_type: MeetingTypeEnum = MeetingTypeEnum.RECURRING_WITH_FIXED_TIME
    webinar_id: UUID

    host_id: Optional[UUID] = None
    topic: Optional[TopicEnum] = None
    recording_url: Optional[str] = None
    zoom_id: Optional[str] = None
    zoom_occurrence_id: Optional[str] = None
    zoom_link: Optional[str] = None
    use_custom_meeting_link: Optional[bool] = None
    custom_meeting_link: Optional[str] = None

    max_capacity: Optional[int] = None
    bookings_count: Optional[int] = None


class FullLiveSession(RawLiveSession):
    webinar: Optional["FullWebinar"] = None
    bookings: Optional[list["FullBooking"]] = None


class Webinar(Model, TimestampMixin):
    id = UUIDField(primary_key=True)
    title = CharField(max_length=255, unique=True)
    description = CharField(max_length=500, unique=True)
    topic = CharEnumField(enum_type=TopicEnum)
    recurrence = CharEnumField(enum_type=RecurrenceEnum)

    host_id: Optional[UUID] = None
    host: ForeignKeyNullableRelation["Authorized"] = ForeignKeyField(
        "models.Authorized",
        related_name="webinars",
        null=True,
        on_delete=SET_NULL,
    )

    sessions: ReverseRelation["LiveSession"]
    duration = IntField(default=60)  # Duration in minutes
    max_capacity = IntField(null=True)
    cover_url = CharField(max_length=255, null=True)

    class Meta:
        table = "webinar"


class RawWebinar(BaseModel):
    """
    A representation of the raw webinar data model.
    """

    model_config = ConfigDict(from_attributes=True)

    id: UUID
    created_at: datetime
    updated_at: datetime
    title: str
    description: str
    topic: TopicEnum
    recurrence: RecurrenceEnum
    host_id: Optional[UUID] = None
    cover_url: Optional[str] = None
    duration: int
    max_capacity: Optional[int] = 250


class FullWebinar(RawWebinar):
    host: Optional["RawAuthorized"] = None
    sessions: Optional[list["FullLiveSession"]] = None
