from uuid import UUID
from ciba_participant.classes.models import (
    Webinar,
    FullWebinar,
    RawLiveSession,
)

from ciba_participant.participant.models import RawAuthorized


async def process(*, webinar_id: UUID) -> FullWebinar:
    webinar = await Webinar.get_or_none(id=webinar_id).prefetch_related(
        "sessions", "host"
    )

    if webinar is None:
        raise Exception("Webinar not found")

    host: RawAuthorized | None = None

    if webinar.host is not None:
        host = RawAuthorized.model_validate(webinar.host)

    sessions: list[RawLiveSession] = []

    for session in webinar.sessions:
        sessions.append(RawLiveSession.model_validate(session))

    return FullWebinar(
        id=webinar.id,
        created_at=webinar.created_at,
        updated_at=webinar.updated_at,
        title=webinar.title,
        description=webinar.description,
        topic=webinar.topic,
        recurrence=webinar.recurrence,
        cover_url=webinar.cover_url,
        duration=webinar.duration,
        max_capacity=webinar.max_capacity,
        sessions=sessions,
        host=host,
    )
