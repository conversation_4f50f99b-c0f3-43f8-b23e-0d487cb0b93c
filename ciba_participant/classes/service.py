from uuid import UUID
from datetime import datetime

from ciba_participant.error_messages.classes import ERROR_PERMISSION
from ciba_participant.classes.errors import HostPermissionError
from ciba_participant.classes.models import (
    Booking,
    BookingStatusEnum,
    LiveSession,
    TimeOfDayEnum,
)
from ciba_participant.cohort.models import CohortProgramModules
from ciba_participant.participant.crud import AuthorizedRepository


async def get_live_session_ids_by_participant(participant_id) -> list[UUID]:
    bookings = await Booking.filter(participant_id=participant_id).prefetch_related(
        "live_session"
    )

    return list(set([booking.live_session.id for booking in bookings]))


async def get_sessions_by_time_of_day(timezone_str: str, time_of_day: TimeOfDayEnum):
    """
    Get live sessions based on the time of day.
    """
    match time_of_day:
        case TimeOfDayEnum.MORNING:
            query_raw = f"""
                SELECT *
                FROM live_session
                WHERE EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE '{timezone_str}') < 12
            """
        case TimeOfDayEnum.AFTERNOON:
            query_raw = f"""
                SELECT *
                FROM live_session
                WHERE EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE '{timezone_str}') >= 12
                  AND EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE '{timezone_str}') < 18
            """
        case TimeOfDayEnum.EVENING:
            query_raw = f"""
                SELECT *
                FROM live_session
                WHERE EXTRACT(HOUR FROM meeting_start_time AT TIME ZONE '{timezone_str}') >= 18
            """
        case _:
            raise ValueError("Unknown period")

    sessions = await LiveSession.raw(query_raw)
    return sessions


def check_bookings_in_module(
    cohort_module: CohortProgramModules,
    bookings: list[Booking],
) -> Booking | None:
    """
    Checks and selects a booking within a specific cohort module's date range.

    This function iterates through a list of bookings and finds a booking that falls
    within the start and end dates of a given cohort module. It prioritizes bookings
    with attended or recorded status.

    Args:
        cohort_module (CohortProgramModules): The cohort module with start and end dates.
        bookings (list[Booking]): A list of bookings to check against the module's date range.

    Returns:
        Booking | None: A booking that meets the date range criteria.
        - If multiple bookings exist, it returns a booking with 'ATTENDED' or
          'WATCHED_RECORDING' status if available.
        - Returns None if no suitable booking is found.

    """
    from_date: datetime = cohort_module.started_at
    to_date: datetime = cohort_module.ended_at

    booking_value = None
    for booking in bookings:
        if from_date <= booking.live_session.meeting_start_time <= to_date:
            booking_value = booking
            if booking.status in [
                BookingStatusEnum.ATTENDED,
                BookingStatusEnum.WATCHED_RECORDING,
            ]:
                return booking_value

    return booking_value


async def check_class_admin(
    authorized_id: UUID,
) -> bool:
    """
    Check if the user is classes admin.
    """
    class_admins = await AuthorizedRepository.get_class_admins()
    class_admin_ids = [admin.id for admin in class_admins]

    if authorized_id not in class_admin_ids:
        raise HostPermissionError(message=ERROR_PERMISSION)

    return True
