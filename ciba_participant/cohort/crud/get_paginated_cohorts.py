import math
from enum import StrEnum, auto
from typing import Optional
from uuid import UUID

import pendulum
from pydantic import BaseModel
from tortoise.functions import Count, Max
from tortoise.queryset import QuerySet

from ciba_participant.cohort.models import (
    Cohort,
    FullCohort,
    RawCohortProgramModules,
    CohortStatusEnum,
)

# No need for RawLiveSession import
from ciba_participant.participant.models import RawAuthorized, RawParticipant
from ciba_participant.program.models import RawProgram
from ciba_participant.log.logging import logger


class Include(StrEnum):
    program = auto()
    participants = auto()
    created_by = auto()
    program_modules = auto()


class CohortStateFilter(StrEnum):
    all = auto()
    empty = auto()
    not_empty = "not_empty"


class CohortStatusFilter(StrEnum):
    all = auto()
    active = auto()
    ending = auto()
    completed = auto()
    pending = auto()


class FilterInput(BaseModel):
    name_like: Optional[str] = None
    created_by: Optional[UUID] = None
    program_id: Optional[UUID] = None
    cohort_state: CohortStateFilter = CohortStateFilter.not_empty
    cohort_status: CohortStatusFilter = CohortStatusFilter.all


class GetCohortsOutput(BaseModel):
    cohorts: list[FullCohort]
    total_pages: int


def apply_empty_state_filter(
    query: QuerySet, include: set[Include], state: CohortStateFilter
):
    if Include.participants not in include or state == CohortStateFilter.all:
        return query

    query = query.annotate(participants_count=Count(Include.participants))

    if state == CohortStateFilter.not_empty:
        return query.filter(participants_count__gt=0)

    return query.filter(participants_count=0)


def apply_status_filter(
    query: QuerySet, include: set[Include], status: CohortStatusFilter
) -> QuerySet:
    """ """
    if Include.program_modules not in include or status == CohortStatusFilter.all:
        return query

    if status == CohortStatusFilter.active:
        return query.filter(
            status=CohortStatusEnum.ACTIVE.value, started_at__lte=pendulum.now()
        )

    if status == CohortStatusFilter.ending:
        days_ahead = 28
        current_date = pendulum.now()
        cutoff_date = current_date.add(days=days_ahead)

        return (
            query.filter(
                status=CohortStatusEnum.ACTIVE.value,
                latest_end_date__gte=current_date,
                latest_end_date__lte=cutoff_date,
            )
            .annotate(latest_end_date=Max("program_modules__ended_at"))
            .all()
        )

    if status == CohortStatusFilter.completed:
        return query.filter(Q(status=CohortStatusEnum.COMPLETED.value)|)

    if status == CohortStatusFilter.pending:
        return query.filter(
            status=CohortStatusEnum.ACTIVE.value, started_at__gt=pendulum.now()
        )

    return query


async def process(
    *,
    page: int,
    per_page: int,
    include: set[Include],
    filters: Optional[FilterInput] = None,
) -> GetCohortsOutput:
    offset = (page - 1) * per_page

    query = Cohort.all()

    if include:
        # Create a list of prefetch objects, excluding participants
        prefetch_objects = [inc for inc in include if inc != Include.participants]

        # We'll fetch participants separately for each cohort
        if prefetch_objects:
            query = query.prefetch_related(*prefetch_objects)

    if filters:
        if filters.name_like:
            query = query.filter(name__icontains=filters.name_like)

        if filters.created_by:
            query = query.filter(created_by_id=filters.created_by)

        if filters.program_id:
            query = query.filter(program_id=filters.program_id)

        query = apply_empty_state_filter(query, include, filters.cohort_state)
        query = apply_status_filter(query, include, filters.cohort_status)

    total_cohorts = await query.count() if await query.exists() else 0
    total_pages = math.ceil(total_cohorts / per_page)

    cohorts = (
        await query.offset(offset)
        .limit(per_page)
        .order_by("-started_at", "-created_at")
    )

    cohorts_output: list[FullCohort] = []

    for cohort in cohorts:
        program_output: Optional[RawProgram] = None

        if Include.program in include:
            try:
                if hasattr(cohort, "program") and cohort.program:
                    program_output = RawProgram.model_validate(cohort.program)
            except Exception as e:
                logger.warning(f"Error processing program for cohort {cohort.id}: {e}")

        participants_output: Optional[list[RawParticipant]] = None

        if Include.participants in include:
            try:
                # Use the active_participants property to get only active participants
                active_participants = await cohort.active_participants

                # Convert to RawParticipant objects if there are any active participants
                if active_participants:
                    participants_output = [
                        RawParticipant.model_validate(member)
                        for member in active_participants
                    ]
                else:
                    # If no active participants, set to empty list instead of None
                    participants_output = []
            except Exception as e:
                logger.warning(
                    f"Error processing participants for cohort {cohort.id}: {e}"
                )
                participants_output = []

        created_by_output: Optional[RawAuthorized] = None

        if Include.created_by in include:
            try:
                if hasattr(cohort, "created_by") and cohort.created_by:
                    created_by_output = RawAuthorized.model_validate(cohort.created_by)
            except Exception as e:
                logger.warning(
                    f"Error processing created_by for cohort {cohort.id}: {e}"
                )

        program_modules_output: Optional[list[RawCohortProgramModules]] = None

        if Include.program_modules in include:
            try:
                # Check if program_modules is loaded and not empty
                if hasattr(cohort, "program_modules") and cohort.program_modules:
                    program_modules_output = [
                        RawCohortProgramModules.model_validate(module)
                        for module in cohort.program_modules
                    ]
                else:
                    # If no program modules, set to empty list instead of None
                    program_modules_output = []
            except Exception as e:
                # Log the error and continue with an empty list
                logger.warning(
                    f"Error processing program modules for cohort {cohort.id}: {e}"
                )
                program_modules_output = []

        # Create a dictionary with the required fields
        cohort_data = {
            "id": cohort.id,
            "created_at": cohort.created_at,
            "updated_at": cohort.updated_at,
            "name": cohort.name,
            "started_at": cohort.started_at,
            "limit": cohort.limit,
            "status": cohort.status,
            "end_date": await cohort.end_date,
            "program_id": cohort.program_id,
            "created_by_id": cohort.created_by_id,
            "program": program_output,
            "participants": participants_output,
            "created_by": created_by_output,
            "program_modules": program_modules_output,
        }

        # Create the FullCohort object
        cohorts_output.append(FullCohort(**cohort_data))

    return GetCohortsOutput(cohorts=cohorts_output, total_pages=total_pages)
