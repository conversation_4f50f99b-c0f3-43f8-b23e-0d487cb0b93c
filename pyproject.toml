[project]
name = "ciba-participant"
version = "1.0.0"
description = "Core of participant"
authors = [
    { name = "blesiv", email = "<EMAIL>" }
]
readme = "README.md"
requires-python = ">=3.12"

dependencies = [
    "aerich>=0.8.0",
    "asyncpg>=0.30.0",
    "aws-encryption-sdk>=4.0.1",
    "boto3>=1.35.36",
    "click>=8.1.7",
    "cryptography>=44.0.1",
    "fastapi[standard]>=0.115.12",
    "h11>=0.16.0",
    "holidays==0.54",
    "httpx>=0.28.1",
    "loguru>=0.7.2",
    "pandas>=2.2.2",
    "pendulum>=3.0.0",
    "pre-commit>=4.2.0",
    "psycopg>=3.1.19",
    "psycopg-pool>=3.2.2",
    "pycryptodome>=3.20.0",
    "pydantic>=2.9.2",
    "pydantic-settings>=2.7.0",
    "sendgrid>=6.11.0",
    "setuptools>=78.1.1",
    "stackprinter>=0.2.12",
    "starlette>=0.37.2",
    "strawberry-graphql>=0.266.0",
    "tabulate>=0.9.0",
    "tortoise-orm>=0.21.7",
]

[tool.setuptools.packages.find]
where = ["."]
include = ["ciba_participant*"]
exclude = ["migrations*"]

[tool.mypy]
[[tool.mypy.overrides]]
module = "pypika.*"
ignore_missing_imports = true

[[tool.mypy.overrides]]
module = "testcontainers.*"
ignore_missing_imports = true

[tool.aerich]
tortoise_orm = "ciba_participant.TORTOISE_ORM"
location = "./migrations"
src_folder = "./."

[tool.pyright]
typeCheckingMode = "standard"
reportIncompatibleVariableOverride = "none"

[build-system]
requires = ["setuptools>=42"]
build-backend = "setuptools.build_meta"

[tool.pytest.ini_options]
minversion = "7.0"
anyio_backend = "asyncio"
addopts = "-ra"
testpaths = [
    "tests/unit/settings", # Needs to be run first, otherwise it will screw other tests
    "tests/unit/activity",
    "tests/unit/classes",
    "tests/unit/cohort",
    "tests/unit/common",
    "tests/unit/content_library",
    "tests/unit/notifications",
    "tests/unit/participant",
    "tests/unit/schedule_manager",
    "tests/integration",
]
asyncio_mode = "auto"
asyncio_default_fixture_loop_scope = "function"
filterwarnings = [
    "ignore::UserWarning",
    "ignore::DeprecationWarning",
]

[tool.uv]
dev-dependencies = [
    "asgi-lifespan>=2.1.0",
    "bandit>=1.8.3",
    "black>=24.4.2",
    "flake8-bugbear>=24.4.26",
    "freezegun>=1.5.1",
    "httpx>=0.27.0",
    "jupyter>=1.1.1",
    "matplotlib>=3.9.1",
    "mimesis>=18.0.0",
    "progressbar2>=4.5.0",
    "pyright>=1.1.376",
    "pyspark>=3.5.1",
    "pytest>=8.3.5",
    "pytest-asyncio>=1.0.0",
    "pytest-cov>=4.1.0",
    "pytest-mock>=3.14.0",
    "respx>=0.22.0",
    "ruff>=0.9.7",
    "sorcery>=0.2.2",
    "sqlalchemy>=2.0.31",
    "testcontainers>=4.5.1",
    "tornado==6.5.0",
]
