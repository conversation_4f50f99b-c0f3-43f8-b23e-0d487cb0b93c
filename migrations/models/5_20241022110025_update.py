from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "participant_activity" ADD "live_session_id" UUID;
        ALTER TABLE "participant_activity" ADD CONSTRAINT "fk_particip_live_ses_f0377fd6" FOREIGN KEY ("live_session_id") REFERENCES "live_session" ("id") ON DELETE NO ACTION;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "participant_activity" DROP CONSTRAINT "fk_particip_live_ses_f0377fd6";
        ALTER TABLE "participant_activity" DROP COLUMN "live_session_id";"""
