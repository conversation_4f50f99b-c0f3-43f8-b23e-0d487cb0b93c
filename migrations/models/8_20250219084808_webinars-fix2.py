from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX IF EXISTS "uid_booking_webinar_c519d4";
        CREATE UNIQUE INDEX IF NOT EXISTS "uid_booking_live_se_c73552" ON "booking" ("live_session_id", "participant_id");"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        DROP INDEX "uid_booking_live_se_c73552";
        CREATE UNIQUE INDEX "uid_booking_webinar_c519d4" ON "booking" ("webinar", "participant_id");"""
