from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "webinar" DROP CONSTRAINT IF EXISTS "webinar_description_key";
        ALTER TABLE "webinar" DROP CONSTRAINT IF EXISTS "webinar_title_key";
        ALTER TABLE "webinar" ALTER COLUMN "recurrence" TYPE VARCHAR(10) USING "recurrence"::VARCHAR(10);
        COMMENT ON COLUMN "webinar"."recurrence" IS 'WEEKLY: weekly
BI_WEEKLY: bi_weekly
TRI_WEEKLY: tri_weekly
MONTHLY: monthly';"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        COMMENT ON COLUMN "webinar"."recurrence" IS 'MONTHLY: monthly';
        ALTER TABLE "webinar" ALTER COLUMN "recurrence" TYPE VARCHAR(7) USING "recurrence"::VARCHAR(7);"""
