from tortoise import BaseDBAsyncClient


async def upgrade(db: BaseDBAsyncClient) -> str:
    return """
        --ALTER TABLE "booking" DROP CONSTRAINT "fk_booking_webinar_af9b3d2e";
        ALTER TABLE "booking" ADD "live_session_id" UUID NOT NULL;
        ALTER TABLE "booking" DROP COLUMN "webinar_id";
        ALTER TABLE "booking" ADD CONSTRAINT "fk_booking_live_ses_d55a42db" FOREIGN KEY ("live_session_id") REFERENCES "live_session" ("id") ON DELETE CASCADE;"""


async def downgrade(db: BaseDBAsyncClient) -> str:
    return """
        ALTER TABLE "booking" DROP CONSTRAINT "fk_booking_live_ses_d55a42db";
        ALTER TABLE "booking" ADD "webinar_id" UUID NOT NULL;
        ALTER TABLE "booking" DROP COLUMN "live_session_id";
        ALTER TABLE "booking" ADD CONSTRAINT "fk_booking_webinar_af9b3d2e" FOREIGN KEY ("webinar_id") REFERENCES "webinar" ("id") ON DELETE CASCADE;"""
