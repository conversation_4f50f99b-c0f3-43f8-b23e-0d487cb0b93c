from uuid import UUID

from ciba_participant.cohort.crud import CohortRepository
from ciba_participant.cohort.models import Cohort
from ciba_participant.common.db import init_db, close_db


async def main():
    await init_db()



    cohort = await CohortRepository.end_cohort(
        cohort_id=UUID("d928f0dd-8513-492d-8089-a8daa8027b08")
    )

    cohort = await Cohort.filter(
        id=UUID("d928f0dd-8513-492d-8089-a8daa8027b08")
    ).first()

    print(cohort.status)

    await close_db()


if __name__ == "__main__":
    import asyncio

    asyncio.run(main())
